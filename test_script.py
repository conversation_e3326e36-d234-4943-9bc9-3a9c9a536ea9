#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Python脚本
演示conda环境的使用
"""

import sys
import platform
import os
import torch

def main():
    print("=" * 50)
    print("Python环境信息")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查是否在conda环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"当前conda环境: {conda_env}")
    else:
        print("未检测到conda环境")
    
    # 尝试导入一些常用包，重点测试torch
    packages_to_test = ['numpy', 'pandas', 'matplotlib', 'requests', 'torch', 'torchvision', 'torchaudio']

    print("\n" + "=" * 50)
    print("包可用性检查")
    print("=" * 50)

    for package in packages_to_test:
        try:
            imported_module = __import__(package)
            if package == 'torch':
                print(f"✅ {package}: 已安装 (版本: {imported_module.__version__})")
                # 检查CUDA支持
                if hasattr(imported_module, 'cuda') and imported_module.cuda.is_available():
                    print(f"   🚀 CUDA可用: {imported_module.cuda.get_device_name(0)}")
                    print(f"   📊 CUDA版本: {imported_module.version.cuda}")
                else:
                    print(f"   💻 仅CPU版本")
            elif hasattr(imported_module, '__version__'):
                print(f"✅ {package}: 已安装 (版本: {imported_module.__version__})")
            else:
                print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")

    # 如果torch可用，进行简单测试
    try:
        import torch
        print("\n" + "=" * 50)
        print("PyTorch功能测试")
        print("=" * 50)

        # 创建一个简单的张量
        x = torch.randn(3, 3)
        print(f"创建随机张量 (3x3):")
        print(x)

        # 测试基本运算
        y = x + 1
        print(f"\n张量 + 1:")
        print(y)

        # 测试矩阵乘法
        z = torch.mm(x, y)
        print(f"\n矩阵乘法结果:")
        print(z)

        print(f"\n✅ PyTorch基本功能正常！")

    except Exception as e:
        print(f"\n❌ PyTorch测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
