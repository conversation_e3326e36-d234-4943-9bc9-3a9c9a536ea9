#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Python脚本
演示conda环境的使用
"""

import sys
import platform
import os

def main():
    print("=" * 50)
    print("Python环境信息")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查是否在conda环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"当前conda环境: {conda_env}")
    else:
        print("未检测到conda环境")
    
    # 尝试导入一些常用包
    packages_to_test = ['numpy', 'pandas', 'matplotlib', 'requests']
    
    print("\n" + "=" * 50)
    print("包可用性检查")
    print("=" * 50)
    
    for package in packages_to_test:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
