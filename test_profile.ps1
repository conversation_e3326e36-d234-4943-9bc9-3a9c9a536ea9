# PowerShell Profile - Safe Version
# This profile does not auto-initialize conda to avoid errors

# Function to manually initialize conda when needed
function Initialize-Conda {
    if (Test-Path "D:\Anaconda\Scripts\conda.exe") {
        $env:PATH = "D:\Anaconda\Scripts;D:\Anaconda;D:\Anaconda\Library\bin;" + $env:PATH
        Write-Host "Conda path added to environment. You can now use conda commands."
        Write-Host "To activate base environment, run: conda activate base"
    } else {
        Write-Warning "Conda not found at D:\Anaconda\Scripts\conda.exe"
    }
}

# Alias for convenience
Set-Alias -Name init-conda -Value Initialize-Conda

Write-Host "PowerShell profile loaded successfully!"
Write-Host "To use conda, run: Initialize-Conda or init-conda"
